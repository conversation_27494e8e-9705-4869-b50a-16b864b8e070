"use client"

import { useState } from 'react'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Trash2, 
  AlertTriangle, 
  Save, 
  X, 
  Info,
  CheckCircle,
  Clock
} from 'lucide-react'

interface EnhancedConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description: string
  itemName?: string
  itemType?: string
  confirmText?: string
  cancelText?: string
  saveText?: string
  variant?: 'default' | 'destructive' | 'warning'
  showSaveOption?: boolean
  onConfirm: () => void
  onSave?: () => void
  additionalInfo?: string[]
  icon?: 'trash' | 'warning' | 'info' | 'success' | 'clock'
}

export function EnhancedConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  itemName,
  itemType = 'العنصر',
  confirmText = 'تأكيد الحذف',
  cancelText = 'إلغاء',
  saveText = 'حفظ القائمة',
  variant = 'destructive',
  showSaveOption = true,
  onConfirm,
  onSave,
  additionalInfo = [],
  icon = 'trash'
}: EnhancedConfirmDialogProps) {
  const [isConfirming, setIsConfirming] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const getIcon = () => {
    const iconClass = "h-12 w-12 mx-auto mb-4"
    switch (icon) {
      case 'warning':
        return <AlertTriangle className={`${iconClass} text-yellow-500`} />
      case 'info':
        return <Info className={`${iconClass} text-blue-500`} />
      case 'success':
        return <CheckCircle className={`${iconClass} text-green-500`} />
      case 'clock':
        return <Clock className={`${iconClass} text-orange-500`} />
      default:
        return <Trash2 className={`${iconClass} text-red-500`} />
    }
  }

  const getVariantStyles = () => {
    switch (variant) {
      case 'warning':
        return {
          headerBg: 'bg-yellow-50 dark:bg-yellow-900/20',
          confirmBtn: 'bg-yellow-600 hover:bg-yellow-700 text-white'
        }
      case 'default':
        return {
          headerBg: 'bg-blue-50 dark:bg-blue-900/20',
          confirmBtn: 'bg-blue-600 hover:bg-blue-700 text-white'
        }
      default:
        return {
          headerBg: 'bg-red-50 dark:bg-red-900/20',
          confirmBtn: 'bg-red-600 hover:bg-red-700 text-white'
        }
    }
  }

  const handleConfirm = async () => {
    setIsConfirming(true)
    try {
      await onConfirm()
      onOpenChange(false)
    } finally {
      setIsConfirming(false)
    }
  }

  const handleSave = async () => {
    if (!onSave) return
    
    setIsSaving(true)
    try {
      await onSave()
      onOpenChange(false)
    } finally {
      setIsSaving(false)
    }
  }

  const styles = getVariantStyles()

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="mobile-card max-w-md sm:max-w-lg">
        {/* Header with Icon */}
        <div className={`${styles.headerBg} -m-6 mb-6 p-6 rounded-t-lg`}>
          {getIcon()}
          <AlertDialogHeader className="text-center">
            <AlertDialogTitle className="mobile-text-lg sm:text-xl font-bold text-gray-900 dark:text-white arabic-text">
              {title}
            </AlertDialogTitle>
          </AlertDialogHeader>
        </div>

        {/* Content */}
        <div className="space-y-4">
          <AlertDialogDescription className="mobile-text-base text-gray-600 dark:text-gray-300 arabic-text text-center">
            {description}
          </AlertDialogDescription>

          {/* Item Details */}
          {itemName && (
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border-r-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="mobile-text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    {itemType}:
                  </p>
                  <p className="mobile-text-base font-semibold text-gray-900 dark:text-white arabic-text">
                    {itemName}
                  </p>
                </div>
                <Badge variant="outline" className="mobile-text-sm">
                  {itemType}
                </Badge>
              </div>
            </div>
          )}

          {/* Additional Information */}
          {additionalInfo.length > 0 && (
            <div className="space-y-2">
              <p className="mobile-text-sm font-medium text-gray-700 dark:text-gray-300 arabic-text">
                معلومات إضافية:
              </p>
              <ul className="space-y-1">
                {additionalInfo.map((info, index) => (
                  <li key={index} className="mobile-text-sm text-gray-600 dark:text-gray-400 arabic-text flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 flex-shrink-0"></span>
                    {info}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Warning Message */}
          {variant === 'destructive' && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />
                <p className="mobile-text-sm text-red-700 dark:text-red-300 arabic-text">
                  تحذير: هذا الإجراء لا يمكن التراجع عنه
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <AlertDialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-3 mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="mobile-btn w-full sm:w-auto order-3 sm:order-1"
            disabled={isConfirming || isSaving}
          >
            <X className="h-4 w-4 mr-2" />
            {cancelText}
          </Button>

          {showSaveOption && onSave && (
            <Button
              variant="outline"
              onClick={handleSave}
              disabled={isConfirming || isSaving}
              className="mobile-btn w-full sm:w-auto order-2 border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
            >
              {isSaving ? (
                <div className="mobile-spinner w-4 h-4 mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {saveText}
            </Button>
          )}

          <Button
            onClick={handleConfirm}
            disabled={isConfirming || isSaving}
            className={`mobile-btn w-full sm:w-auto order-1 sm:order-3 ${styles.confirmBtn}`}
          >
            {isConfirming ? (
              <div className="mobile-spinner w-4 h-4 mr-2"></div>
            ) : (
              <Trash2 className="h-4 w-4 mr-2" />
            )}
            {confirmText}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Hook لاستخدام مربع الحوار بسهولة
export function useEnhancedConfirmDialog() {
  const [dialogState, setDialogState] = useState({
    open: false,
    title: '',
    description: '',
    itemName: '',
    itemType: 'العنصر',
    onConfirm: () => {},
    onSave: undefined as (() => void) | undefined,
    variant: 'destructive' as 'default' | 'destructive' | 'warning',
    showSaveOption: true,
    additionalInfo: [] as string[]
  })

  const showDialog = (options: Partial<typeof dialogState>) => {
    setDialogState(prev => ({ ...prev, ...options, open: true }))
  }

  const hideDialog = () => {
    setDialogState(prev => ({ ...prev, open: false }))
  }

  const DialogComponent = () => (
    <EnhancedConfirmDialog
      {...dialogState}
      onOpenChange={hideDialog}
    />
  )

  return {
    showDialog,
    hideDialog,
    DialogComponent
  }
}

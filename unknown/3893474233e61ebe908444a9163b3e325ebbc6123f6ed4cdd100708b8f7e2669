"use client"

import { useState, useEffect, useRef } from 'react'
import { useTranslation } from "@/hooks/useTranslation"
import { useStats } from "@/hooks/useStats"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Users, 
  School, 
  ShoppingBag, 
  Star,
  TrendingUp
} from "lucide-react"

interface StatItemProps {
  icon: React.ReactNode
  number: string
  label: string
  delay: number
  color: string
}

function StatItem({ icon, number, label, delay, color }: StatItemProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [animatedNumber, setAnimatedNumber] = useState('0')
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          // Animate number counting
          const finalNumber = number.replace(/[^0-9]/g, '')
          const numValue = parseInt(finalNumber)
          if (!isNaN(numValue)) {
            let current = 0
            const increment = numValue / 50
            const timer = setInterval(() => {
              current += increment
              if (current >= numValue) {
                setAnimatedNumber(number)
                clearInterval(timer)
              } else {
                setAnimatedNumber(Math.floor(current).toString() + (number.includes('+') ? '+' : '') + (number.includes('%') ? '%' : ''))
              }
            }, 30)
          } else {
            setAnimatedNumber(number)
          }
        }
      },
      { threshold: 0.1 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [number])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <Card className="mobile-card text-center hover:shadow-lg transition-all duration-300 border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
        <CardContent className="mobile-spacing p-6 sm:p-8">
          <div className={`inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 rounded-full mb-3 sm:mb-4 ${color}`}>
            {icon}
          </div>
          <div className="mobile-text-2xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-2 arabic-text">
            {animatedNumber}
          </div>
          <div className="mobile-text-sm sm:text-base text-gray-600 dark:text-gray-300 font-medium arabic-text">
            {label}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function StatsSection() {
  const { t } = useTranslation()
  const { stats: statsData, loading } = useStats()

  const stats = [
    {
      icon: <Users className="w-6 h-6 sm:w-8 sm:h-8 text-white" />,
      number: loading ? '...' : `${statsData.customers.toLocaleString()}+`,
      label: t('home.stats.customers.label'),
      color: "bg-gradient-to-br from-blue-500 to-blue-600",
      delay: 0
    },
    {
      icon: <School className="w-6 h-6 sm:w-8 sm:h-8 text-white" />,
      number: loading ? '...' : `${statsData.schools}+`,
      label: t('home.stats.schools.label'),
      color: "bg-gradient-to-br from-green-500 to-green-600",
      delay: 200
    },
    {
      icon: <ShoppingBag className="w-6 h-6 sm:w-8 sm:h-8 text-white" />,
      number: loading ? '...' : `${statsData.orders.toLocaleString()}+`,
      label: t('home.stats.orders.label'),
      color: "bg-gradient-to-br from-purple-500 to-purple-600",
      delay: 400
    },
    {
      icon: <Star className="w-6 h-6 sm:w-8 sm:h-8 text-white" />,
      number: loading ? '...' : `${statsData.satisfaction}%`,
      label: t('home.stats.satisfaction.label'),
      color: "bg-gradient-to-br from-yellow-500 to-yellow-600",
      delay: 600
    }
  ]

  return (
    <section className="py-12 sm:py-16 md:py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="mobile-container">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-2 sm:px-4 sm:py-2 rounded-full mobile-text-sm font-medium mb-4">
            <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
            إحصائيات المنصة
          </div>
          <h2 className="mobile-text-2xl sm:text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text">
            {t('home.stats.title')}
          </h2>
          <p className="mobile-text-base sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto arabic-text">
            {t('home.stats.subtitle')}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="mobile-grid gap-4 sm:gap-6 md:gap-8">
          {stats.map((stat, index) => (
            <StatItem
              key={index}
              icon={stat.icon}
              number={stat.number}
              label={stat.label}
              delay={stat.delay}
              color={stat.color}
            />
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12 sm:mt-16">
          <p className="mobile-text-sm text-gray-500 dark:text-gray-400 arabic-text">
            آخر تحديث: {new Date().toLocaleDateString('ar-MA')}
          </p>
        </div>
      </div>
    </section>
  )
}

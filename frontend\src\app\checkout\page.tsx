"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { Navigation } from '@/components/Navigation'
import { CheckoutManager } from '@/lib/checkout'
import { DynamicFormField } from '@/components/checkout/DynamicFormField'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'




import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { CardDescription } from '@/components/ui/card'

import { 
  CreditCard, Shield,
  Truck, ArrowLeft,
  ArrowRight
} from 'lucide-react'

// أنواع البيانات
interface CheckoutData {
  fields: Array<{
    id: string
    label: string
    required: boolean
    enabled: boolean
    section: string
    order: number
  }>
  deliveryOptions: Array<{
    id: string
    name: string
    description: string
    price: number
    enabled: boolean
    order: number
    estimatedDays: string
    restrictions?: {
      minOrderValue?: number
    }
  }>
  paymentMethods: Array<{
    id: string
    name: string
    description: string
    enabled: boolean
    order: number
    config?: {
      additionalFields?: boolean
    }
  }>
  general: {
    requireTermsAcceptance: boolean
    enableSpecialInstructions: boolean
    showOrderSummary: boolean
  }
}

export default function CheckoutPage() {
  const { } = useAuth() // ✅ إصلاح: إزالة متغيرات غير مستخدمة
  const { } = useCart() // ✅ إصلاح: إزالة متغيرات غير مستخدمة
  const [checkoutData, setCheckoutData] = useState<CheckoutData | null>(null)
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<Record<string, unknown>>({})
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [specialInstructions, setSpecialInstructions] = useState('')
  const [agreeToTerms, setAgreeToTerms] = useState(false)
  const [loading, setLoading] = useState(false)

  // بيانات وهمية للطلب
  const orderSummary = {
    items: [
      { name: 'فستان تخرج كلاسيكي', quantity: 1, price: 450 },
      { name: 'قبعة التخرج', quantity: 1, price: 75 }
    ],
    subtotal: 525,
    tax: 26.25,
    shipping: 25,
    total: 576.25
  }

  // تحميل إعدادات الدفع
  useEffect(() => {
    const load = async () => {
      try {
        const settings = await CheckoutManager.getSettings()
        setCheckoutData(settings)
        
        // تعيين القيم الافتراضية
        if (settings.deliveryOptions.length > 0) {
          const defaultDelivery = settings.deliveryOptions.find(opt => opt.enabled)
          if (defaultDelivery) {
            setSelectedDeliveryOption(defaultDelivery.id)
          }
        }
        
        if (settings.paymentMethods.length > 0) {
          const defaultPayment = settings.paymentMethods.find(method => method.enabled)
          if (defaultPayment) {
            setSelectedPaymentMethod(defaultPayment.id)
          }
        }
      } catch {
        // Error loading checkout settings
      }
    }

    load()
  }, [])

  const updateFormData = (fieldId: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }))
  }

  const validateForm = () => {
    if (!checkoutData) return false

    // التحقق من الحقول المطلوبة
    const requiredFields = checkoutData.fields.filter(field => field.required && field.enabled)
    
    for (const field of requiredFields) {
      if (!formData[field.id] || formData[field.id].toString().trim() === '') {
        alert(`الرجاء إدخال ${field.label}`)
        return false
      }
    }

    // التحقق من طريقة التوصيل
    if (!selectedDeliveryOption) {
      alert('الرجاء اختيار طريقة التوصيل')
      return false
    }

    // التحقق من طريقة الدفع
    if (!selectedPaymentMethod) {
      alert('الرجاء اختيار طريقة الدفع')
      return false
    }

    // التحقق من الموافقة على الشروط
    if (checkoutData.general.requireTermsAcceptance && !agreeToTerms) {
      alert('يرجى الموافقة على الشروط والأحكام')
      return false
    }

    return true
  }

  const handleSubmitOrder = async () => {
    if (!validateForm()) return

    setLoading(true)

    // محاكاة معالجة الطلب
    setTimeout(() => {
      setLoading(false)

      // توجيه حسب طريقة الدفع المختارة
      if (selectedPaymentMethod === 'bank_transfer') {
        // توجيه إلى صفحة الدفع البنكي
        const orderId = 'ORD-' + Date.now()
        const totalAmount = orderSummary.total
        window.location.href = `/payment/bank-transfer?order_id=${orderId}&amount=${totalAmount}`
      } else {
        // توجيه إلى صفحة تأكيد الطلب للطرق الأخرى
        window.location.href = '/order-confirmation'
      }
    }, 2000)
  }

  const steps = [
    { id: 1, name: 'معلومات الشحن', icon: ' ' },
    { id: 2, name: 'طريقة التوصيل', icon: 'Truck' },
    { id: 3, name: 'طريقة الدفع', icon: 'CreditCard' },
    { id: 4, name: "مراجعة الطلب", icon: "Check" }
  ]

  const getStepIcon = (iconName: string) => {
    switch (iconName) {
      case ' ': return <CardDescription className="h-4 w-4" />
      case 'Truck': return <Truck className="h-4 w-4" />
      case 'CreditCard': return <CreditCard className="h-4 w-4" />
      case ' ': return <CardDescription className="h-4 w-4" />
      default: return <CardDescription className="h-4 w-4" />
    }
  }

  if (!checkoutData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">جاري تحميل صفحة الدفع...</p>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <Button variant="outline" size="sm" asChild className="mb-4">
            <a href="/cart">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة للسلة
            </a>
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            إتمام الطلب 💳
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            أكمل معلوماتك لإتمام عملية الشراء
          </p>
        </div>

        {/* Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CardDescription className="h-5 w-5" />
                  ) : (
                    getStepIcon(step.icon)
                  )}
                </div>
                <span className={`ml-3 text-sm font-medium arabic-text ${
                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                }`}>
                  {step.name}
                </span>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Step 1: Customer rmation */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <CardDescription className="h-5 w-5" />
                    معلومات العميل
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    أدخل معلوماتك الشخصية ومعلومات التوصيل
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Personal rmation */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white arabic-text mb-4">
                      المعلومات الشخصية
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {checkoutData.fields
                        .filter(field => field.section === 'personal' && field.enabled)
                        .sort((a, b) => a.order - b.order)
                        .map(field => (
                          <DynamicFormField
                            key={field.id}
                            field={field}
                            value={formData[field.id]}
                            onChange={(value) => updateFormData(field.id, value)}
                          />
                        ))}
                    </div>
                  </div>

                  {/* Shipping rmation */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white arabic-text mb-4">
                      معلومات التوصيل
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {checkoutData.fields
                        .filter(field => field.section === 'shipping' && field.enabled)
                        .sort((a, b) => a.order - b.order)
                        .map(field => (
                          <DynamicFormField
                            key={field.id}
                            field={field}
                            value={formData[field.id]}
                            onChange={(value) => updateFormData(field.id, value)}
                          />
                        ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={() => setCurrentStep(2)} className="arabic-text">
                      التالي
                      <ArrowLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Delivery Options */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <Truck className="h-5 w-5" />
                    طريقة التوصيل
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    اختر طريقة التوصيل المناسبة لك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={selectedDeliveryOption} onValueChange={setSelectedDeliveryOption}>
                    <div className="space-y-4">
                      {checkoutData.deliveryOptions
                        .filter(option => option.enabled)
                        .sort((a, b) => a.order - b.order)
                        .map((option) => (
                          <div key={option.id} className="flex items-center space-x-2">
                            <RadioGroupItem value={option.id} id={option.id} />
                            <label htmlFor={option.id} className="flex-1 cursor-pointer">
                              <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                                <div className="flex items-center gap-3">
                                  <Truck className="h-5 w-5 text-blue-600" />
                                  <div>
                                    <p className="font-medium arabic-text">{option.name}</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                                      {option.description} - {option.estimatedDays}
                                    </p>
                                    {option.restrictions?.minOrderValue && (
                                      <p className="text-xs text-orange-600 arabic-text">
                                        الحد الأدنى للطلب: {option.restrictions.minOrderValue} درهم
                                      </p>
                                    )}
                                  </div>
                                </div>
                                <span className="font-bold text-green-600">
                                  {option.price === 0 ? 'مجاني' : `${option.price} درهم`}
                                </span>
                              </div>
                            </label>
                          </div>
                        ))}
                    </div>
                  </RadioGroup>

                  {checkoutData.general.enableSpecialInstructions && (
                    <div className="mt-6">
                      <label htmlFor="instructions" className="arabic-text">تعليمات خاصة (اختياري)</label>
                      <Textarea id="instructions"
                        value={specialInstructions}
                        onChange={(e) => setSpecialInstructions(e.target.value)}
                        placeholder="أي تعليمات خاصة للتوصيل..."
                        className="arabic-text"
                      />
                    </div>
                  )}

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(1)} className="arabic-text">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <Button onClick={() => setCurrentStep(3)} className="arabic-text">
                      التالي
                      <ArrowLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Payment Method */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <CreditCard className="h-5 w-5" />
                    طريقة الدفع
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    اختر طريقة الدفع المفضلة لديك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                    <div className="space-y-4">
                      {checkoutData.paymentMethods
                        .filter(method => method.enabled)
                        .sort((a, b) => a.order - b.order)
                        .map((method) => (
                          <div key={method.id} className="flex items-center space-x-2">
                            <RadioGroupItem value={method.id} id={method.id} />
                            <label htmlFor={method.id} className="flex-1 cursor-pointer">
                              <div className="flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                                <CreditCard className="h-5 w-5 text-blue-600" />
                                <div>
                                  <p className="font-medium arabic-text">{method.name}</p>
                                  <p className="text-sm text-gray-600 dark:text-gray-400 arabic-text">
                                    {method.description}
                                  </p>
                                </div>
                              </div>
                            </label>
                          </div>
                        ))}
                    </div>
                  </RadioGroup>

                  {/* Payment Method Specific Fields */}
                  {(() => {
                    const selectedMethod = checkoutData.paymentMethods.find(m => m.id === selectedPaymentMethod)
                    if (selectedMethod?.config?.additionalFields) {
                      return (
                        <div className="mt-6 space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
                          <h3 className="font-medium arabic-text">معلومات الدفع</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {selectedMethod.config.additionalFields.map(field => (
                              <DynamicFormField
                                key={field.id}
                                field={field}
                                value={formData[field.id]}
                                onChange={(value) => updateFormData(field.id, value)}
                              />
                            ))}
                          </div>
                        </div>
                      )
                    }
                    return null
                  })()}

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(2)} className="arabic-text">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <Button onClick={() => setCurrentStep(4)} className="arabic-text">
                      التالي
                      <ArrowLeft className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4: Order Review */}
            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 arabic-text">
                    <CardDescription className="h-5 w-5" />
                    مراجعة الطلب
                  </CardTitle>
                  <CardDescription className="arabic-text">
                    راجع تفاصيل طلبك قبل التأكيد
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Order Items */}
                  <div>
                    <h3 className="font-medium mb-3 arabic-text">المنتجات</h3>
                    <div className="space-y-2">
                      {orderSummary.items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <span className="arabic-text">{item.name} × {item.quantity}</span>
                          <span>{item.price} درهم</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Terms and Conditions */}
                  {checkoutData.general.requireTermsAcceptance && (
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Checkbox
                        id="terms"
                        checked={agreeToTerms}
                        onCheckedChange={setAgreeToTerms}
                      />
                      <label htmlFor="terms" className="text-sm arabic-text">
                        أوافق على <a href="/terms" className="text-blue-600 hover:underline">الشروط والأحكام</a> و
                        <a href="/privacy" className="text-blue-600 hover:underline">سياسة الخصوصية</a>
                      </label>
                    </div>
                  )}

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)} className="arabic-text">
                      <ArrowRight className="h-4 w-4 ml-2" />
                      السابق
                    </Button>
                    <Button 
                      onClick={handleSubmitOrder} 
                      disabled={(checkoutData.general.requireTermsAcceptance && !agreeToTerms) || loading}
                      className="arabic-text"
                    >
                      {loading ? 'جاري المعالجة...' : 'تأكيد الطلب'}
                      <CardDescription className="h-4 w-4 mr-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Summary Sidebar */}
          {checkoutData.general.showOrderSummary && (
            <div className="lg:col-span-1">
              <Card className="sticky top-24">
                <CardHeader>
                  <CardTitle className="arabic-text">ملخص الطلب</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    {orderSummary.items.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="arabic-text">{item.name} × {item.quantity}</span>
                        <span>{item.price} درهم</span>
                      </div>
                    ))}
                  </div>



                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="arabic-text">المجموع الفرعي:</span>
                      <span>{orderSummary.subtotal} درهم</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">الضريبة:</span>
                      <span>{orderSummary.tax} درهم</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic-text">الشحن:</span>
                      <span>{orderSummary.shipping} درهم</span>
                    </div>
                  </div>



                  <div className="flex justify-between font-bold text-lg">
                    <span className="arabic-text">الإجمالي:</span>
                    <span>{orderSummary.total} درهم</span>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Shield className="h-4 w-4" />
                    <span className="arabic-text">دفع آمن ومضمون</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
